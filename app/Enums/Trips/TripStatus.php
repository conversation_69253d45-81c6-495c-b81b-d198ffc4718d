<?php

namespace App\Enums\Trips;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum TripStatus: string implements HasColor, HasIcon, HasLabel
{
    case pending = 'pending';
    case timeout = 'timeout';
    case dispatched = 'dispatched';
    case canceled = 'canceled';
    case canceled_by_admin = 'canceled_by_admin';
    case rejected = 'rejected';
    case assigned = 'assigned';
    case driver_arriving = 'driver_arriving';
    case driver_arrived = 'driver_arrived';
    case no_show = 'no_show';
    case on_trip = 'on_trip';
    case waiting_for_driver_confirmation = 'waiting_for_driver_confirmation';
    case completed = 'completed';

    public function getLabel(): string
    {
        return match ($this) {
            self::pending => 'Pending',
            self::timeout => 'Timeout',
            self::dispatched => 'Dispatched',
            self::canceled => 'Canceled',
            self::canceled_by_admin => 'Canceled by Admin',
            self::rejected => 'Rejected',
            self::assigned => 'Assigned',
            self::driver_arriving => 'Driver Arriving',
            self::driver_arrived => 'Driver Arrived',
            self::no_show => 'No Show',
            self::on_trip => 'On Trip',
            self::waiting_for_driver_confirmation => 'Waiting for Driver Confirmation',
            self::completed => 'Completed',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::pending => 'warning',
            self::timeout => 'gray',
            self::dispatched => 'green',
            self::canceled => 'red',
            self::canceled_by_admin => 'orange',
            self::rejected => 'danger',
            self::assigned => 'info',
            self::driver_arriving => 'amber',
            self::driver_arrived => 'teal',
            self::no_show => 'danger',
            self::on_trip => 'orange',
            self::waiting_for_driver_confirmation => 'purple',
            self::completed => 'success',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::pending => 'heroicon-o-arrow-down-left',
            self::timeout => 'heroicon-o-clock',
            self::dispatched => 'heroicon-o-paper-airplane',
            self::canceled => 'heroicon-o-x-mark',
            self::canceled_by_admin => 'heroicon-o-shield-exclamation',
            self::rejected => 'heroicon-o-x-circle',
            self::assigned => 'heroicon-o-user-circle',
            self::driver_arriving => 'heroicon-o-arrow-trending-up', // 🚗 Arriving
            self::driver_arrived => 'heroicon-o-map-pin', // 📍 Arrived
            self::no_show => 'heroicon-o-eye-slash', // 🙈 No show
            self::on_trip => 'heroicon-o-truck', // 🚕 On the move
            self::waiting_for_driver_confirmation => 'heroicon-o-exclamation-triangle', // ❓ Awaiting action
            self::completed => 'heroicon-o-check-circle', // ✅ Completed
        };
    }
}
