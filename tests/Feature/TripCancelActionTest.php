<?php

namespace Tests\Feature;

use App\Enums\Trips\TripStatus;
use App\Filament\Tables\TripsTable;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\User;
use Filament\Tables\Table;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TripCancelActionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user for authentication
        $this->adminUser = User::factory()->create([
            'type' => 'admin',
            'email' => '<EMAIL>',
        ]);
        
        $this->actingAs($this->adminUser);
    }

    /** @test */
    public function cancel_action_is_visible_for_pending_trips()
    {
        // Create a rider and driver
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        // Create a pending trip
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::pending,
        ]);

        // Create the table instance
        $table = $this->createTable();
        
        // Get the actions for the trip record
        $actions = $table->getActions();
        
        // Find the cancel action
        $cancelAction = collect($actions)->first(fn($action) => $action->getName() === 'cancel');
        
        $this->assertNotNull($cancelAction, 'Cancel action should exist');
        
        // Test visibility for pending trip
        $this->assertTrue(
            $cancelAction->isVisible($trip),
            'Cancel action should be visible for pending trips'
        );
    }

    /** @test */
    public function cancel_action_is_visible_for_dispatched_trips()
    {
        // Create a rider and driver
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        // Create a dispatched trip
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::dispatched,
        ]);

        // Create the table instance
        $table = $this->createTable();
        
        // Get the actions for the trip record
        $actions = $table->getActions();
        
        // Find the cancel action
        $cancelAction = collect($actions)->first(fn($action) => $action->getName() === 'cancel');
        
        $this->assertNotNull($cancelAction, 'Cancel action should exist');
        
        // Test visibility for dispatched trip
        $this->assertTrue(
            $cancelAction->isVisible($trip),
            'Cancel action should be visible for dispatched trips'
        );
    }

    /** @test */
    public function cancel_action_is_not_visible_for_completed_trips()
    {
        // Create a rider and driver
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        // Create a completed trip
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::completed,
        ]);

        // Create the table instance
        $table = $this->createTable();
        
        // Get the actions for the trip record
        $actions = $table->getActions();
        
        // Find the cancel action
        $cancelAction = collect($actions)->first(fn($action) => $action->getName() === 'cancel');
        
        $this->assertNotNull($cancelAction, 'Cancel action should exist');
        
        // Test visibility for completed trip
        $this->assertFalse(
            $cancelAction->isVisible($trip),
            'Cancel action should not be visible for completed trips'
        );
    }

    /** @test */
    public function cancel_action_is_not_visible_for_canceled_trips()
    {
        // Create a rider and driver
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        // Create a canceled trip
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::canceled,
        ]);

        // Create the table instance
        $table = $this->createTable();
        
        // Get the actions for the trip record
        $actions = $table->getActions();
        
        // Find the cancel action
        $cancelAction = collect($actions)->first(fn($action) => $action->getName() === 'cancel');
        
        $this->assertNotNull($cancelAction, 'Cancel action should exist');
        
        // Test visibility for canceled trip
        $this->assertFalse(
            $cancelAction->isVisible($trip),
            'Cancel action should not be visible for canceled trips'
        );
    }

    private function createTable(): Table
    {
        $table = Table::make();
        return TripsTable::make($table);
    }
}
