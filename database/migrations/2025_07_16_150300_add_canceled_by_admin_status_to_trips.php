<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Drop the existing enum constraint
            $table->dropColumn('status');
        });

        Schema::table('trips', function (Blueprint $table) {
            // Re-add with the new canceled_by_admin status
            $table->enum('status', [
                'pending', 'timeout', 'dispatched', 'canceled', 'canceled_by_admin', 'rejected',
                'assigned', 'driver_arriving', 'driver_arrived', 'no_show',
                'on_trip', 'waiting_for_driver_confirmation', 'completed',
            ])->default('pending');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Drop the updated enum
            $table->dropColumn('status');
        });

        Schema::table('trips', function (Blueprint $table) {
            // Restore original enum without canceled_by_admin
            $table->enum('status', [
                'pending', 'timeout', 'dispatched', 'canceled', 'rejected',
                'assigned', 'driver_arriving', 'driver_arrived', 'no_show',
                'on_trip', 'waiting_for_driver_confirmation', 'completed',
            ])->default('pending');
        });
    }
};
