<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Drop the existing enum constraint
            $table->dropColumn('cancelled_by');
        });

        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Re-add with admin option
            $table->enum('cancelled_by', ['rider', 'driver', 'admin'])->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Drop the updated enum
            $table->dropColumn('cancelled_by');
        });

        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Restore original enum
            $table->enum('cancelled_by', ['rider', 'driver'])->after('user_id');
        });
    }
};
